<?php
/**
 * Auth Class
 * 
 * This class handles user authentication, including login, registration,
 * password hashing, and role-based access control.
 */
class Auth {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
        
        // Check for remember me cookie and auto-login if valid
        if (!isset($_SESSION['user_id']) && isset($_COOKIE['remember_token'])) {
            $this->loginWithRememberToken($_COOKIE['remember_token']);
        }
        
        // Check if session has expired
        if (isset($_SESSION['login_time']) && isset($_SESSION['user_id'])) {
            $sessionLifetime = $this->getSessionLifetime();
            
            // Log session information for debugging
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Session check - Current time: ' . time());
                error_log('Session check - Login time: ' . $_SESSION['login_time']);
                error_log('Session check - Session lifetime: ' . $sessionLifetime);
                error_log('Session check - Elapsed time: ' . (time() - $_SESSION['login_time']));
                error_log('Session check - Is Facebook login: ' . (isset($_SESSION['facebook_login']) ? 'Yes' : 'No'));
            }
            
            if (time() - $_SESSION['login_time'] > $sessionLifetime) {
                // Session has expired, log the user out
                error_log('Session expired for user ID: ' . $_SESSION['user_id'] . ' after ' . (time() - $_SESSION['login_time']) . ' seconds');
                $this->logout();
                
                // Redirect to login page with expired message
                if (!isset($_SESSION['session_expired_redirect']) && !defined('DOING_AJAX')) {
                    $_SESSION['session_expired_redirect'] = true;
                    if (defined('BASE_URL')) {
                        header('Location: ' . BASE_URL . '/auth/login?expired=1');
                        exit;
                    }
                }
            } else {
                // Refresh the session timestamp to extend the session
                // This is important for long-running browser sessions
                $_SESSION['login_time'] = time();
            }
        }
    }
    
    /**
     * Get session lifetime from settings
     * 
     * @return int Session lifetime in seconds
     */
    public function getSessionLifetime() {
        // Default to 24 hours (86400 seconds)
        $defaultLifetime = 86400;
        
        try {
            // Check if we're using a Facebook session
            $isFacebookSession = isset($_SESSION['facebook_login']) && $_SESSION['facebook_login'] === true;
            
            // Get the appropriate setting key
            $settingKey = $isFacebookSession ? 'facebook_session_lifetime' : 'session_lifetime';
            
            // Try to get the setting from the database
            $this->db->query('SELECT setting_value FROM system_settings WHERE setting_key = :key');
            $this->db->bind(':key', $settingKey);
            $result = $this->db->single();
            
            if ($result && is_numeric($result->setting_value)) {
                $lifetime = (int)$result->setting_value;
                
                // Log the retrieved session lifetime for debugging
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log('Retrieved ' . ($isFacebookSession ? 'Facebook' : 'regular') . ' session lifetime: ' . $lifetime . ' seconds');
                }
                
                // Ensure the lifetime is at least 5 minutes (300 seconds)
                if ($lifetime < 300) {
                    error_log('Session lifetime too short, using minimum value of 300 seconds');
                    $lifetime = 300;
                }
                
                return $lifetime;
            } else {
                // Log that we couldn't find the setting
                error_log('Could not find ' . $settingKey . ' in system_settings, using default: ' . $defaultLifetime);
            }
        } catch (Exception $e) {
            error_log('Error getting session lifetime: ' . $e->getMessage());
        }
        
        return $defaultLifetime;
    }
    
    /**
     * Get remember me lifetime from settings
     * 
     * @return int Remember me lifetime in seconds
     */
    private function getRememberMeLifetime() {
        // Default to 30 days (2592000 seconds)
        $defaultLifetime = 2592000;
        
        try {
            // Try to get the setting from the database
            $this->db->query('SELECT setting_value FROM system_settings WHERE setting_key = :key');
            $this->db->bind(':key', 'remember_me_lifetime');
            $result = $this->db->single();
            
            if ($result && is_numeric($result->setting_value)) {
                return (int)$result->setting_value;
            }
        } catch (Exception $e) {
            error_log('Error getting remember me lifetime: ' . $e->getMessage());
        }
        
        return $defaultLifetime;
    }
    
    /**
     * Login with remember token
     * 
     * @param string $token Remember token
     * @return bool True if login successful, false otherwise
     */
    private function loginWithRememberToken($token) {
        try {
            // Check if remember_tokens table exists
            $tableExists = false;
            try {
                $this->db->query("SHOW TABLES LIKE 'remember_tokens'");
                $tableExists = (bool)$this->db->single();
            } catch (Exception $e) {
                error_log('Error checking if remember_tokens table exists: ' . $e->getMessage());
                return false;
            }
            
            if (!$tableExists) {
                error_log('Remember tokens table does not exist yet');
                return false;
            }
            
            // Find token in database
            $this->db->query('SELECT rt.user_id, u.role, u.name, u.email 
                             FROM remember_tokens rt 
                             JOIN users u ON rt.user_id = u.id 
                             WHERE rt.token = :token AND rt.expires_at > NOW()');
            $this->db->bind(':token', $token);
            $result = $this->db->single();
            
            if ($result) {
                // Set session variables
                $_SESSION['user_id'] = $result->user_id;
                $_SESSION['user_role'] = $result->role;
                $_SESSION['user_name'] = $result->name;
                $_SESSION['user_email'] = $result->email;
                $_SESSION['login_time'] = time();
                
                // Generate a new token for security
                $newToken = bin2hex(random_bytes(32));
                $lifetime = $this->getRememberMeLifetime();
                $expiresAt = gmdate('Y-m-d H:i:s', time() + $lifetime);
                
                // Update the token in the database
                $this->db->query('UPDATE remember_tokens SET token = :new_token, expires_at = :expires_at WHERE token = :old_token');
                $this->db->bind(':new_token', $newToken);
                $this->db->bind(':expires_at', $expiresAt);
                $this->db->bind(':old_token', $token);
                $this->db->execute();
                
                // Set the new cookie
                setcookie('remember_token', $newToken, time() + $lifetime, '/', '', isset($_SERVER['HTTPS']), true);
                
                // Update last login
                $this->db->query('UPDATE users SET last_login = NOW() WHERE id = :id');
                $this->db->bind(':id', $result->user_id);
                $this->db->execute();
                
                return true;
            }
        } catch (Exception $e) {
            error_log('Error logging in with remember token: ' . $e->getMessage());
        }
        
        // If we get here, the token is invalid or expired
        // Clear the cookie
        setcookie('remember_token', '', time() - 3600, '/', '', isset($_SERVER['HTTPS']), true);
        
        return false;
    }
    
    /**
     * Register a new user
     * 
     * @param string $name User's name
     * @param string $email User's email
     * @param string $password User's password
     * @param string $role User's role (default: 'user')
     * @return bool|int False on failure, user ID on success
     */
    public function register($name, $email, $password, $role = 'user', $phone = null, $address = null, $city = null, $state = null, $zip = null) {
        // Validate inputs
        $name = trim($name);
        $email = filter_var(trim($email), FILTER_SANITIZE_EMAIL);
        
        if (empty($name) || empty($email) || empty($password)) {
            return false; // Required fields missing
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return false; // Invalid email format
        }
        
        // Password strength validation
        if (strlen($password) < 6) {
            return false; // Password too short
        }
        
        // Hash password with stronger algorithm and options
        $hashedPassword = password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
        
        // Check if email already exists
        $this->db->query('SELECT id FROM users WHERE email = :email');
        $this->db->bind(':email', $email);
        $existingUser = $this->db->single();
        
        if ($existingUser) {
            return false; // Email already exists
        }
        
        // Validate role
        $validRoles = ['admin', 'coordinator', 'judge', 'staff', 'user'];
        if (!in_array($role, $validRoles)) {
            $role = 'user'; // Default to user if invalid role provided
        }
        
        // Insert user
        $this->db->query('INSERT INTO users (name, email, password, role, phone, address, city, state, zip, created_at) 
                          VALUES (:name, :email, :password, :role, :phone, :address, :city, :state, :zip, NOW())');
        $this->db->bind(':name', $name);
        $this->db->bind(':email', $email);
        $this->db->bind(':password', $hashedPassword);
        $this->db->bind(':role', $role);
        $this->db->bind(':phone', $phone);
        $this->db->bind(':address', $address);
        $this->db->bind(':city', $city);
        $this->db->bind(':state', $state);
        $this->db->bind(':zip', $zip);
        
        try {
            if ($this->db->execute()) {
                return $this->db->lastInsertId();
            } else {
                error_log('Failed to register user: ' . $email);
                return false;
            }
        } catch (Exception $e) {
            error_log('Exception during user registration: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Register or login a user via Facebook
     * 
     * @param array $fbUserData Facebook user data
     * @param string $accessToken Facebook access token
     * @return bool|int False on failure, user ID on success
     */
    public function facebookAuth($fbUserData, $accessToken = null) {
        // Validate required data
        if (empty($fbUserData['id']) || empty($fbUserData['email'])) {
            error_log('Facebook auth failed: Missing required user data');
            return false;
        }
        
        // First check if user exists by Facebook ID
        $this->db->query('SELECT id, role FROM users WHERE facebook_id = :facebook_id');
        $this->db->bind(':facebook_id', $fbUserData['id']);
        $user = $this->db->single();
        
        // If not found by Facebook ID, check if user exists by email
        if (!$user) {
            $this->db->query('SELECT id, role FROM users WHERE email = :email');
            $this->db->bind(':email', $fbUserData['email']);
            $user = $this->db->single();
        }
        
        if ($user) {
            // User exists, update their info
            $sql = 'UPDATE users SET 
                    facebook_id = :facebook_id,
                    name = :name, 
                    email = :email,';
            
            // Add facebook_token to the update if provided
            if ($accessToken) {
                $sql .= ' facebook_token = :facebook_token,';
            }
            
            $sql .= ' last_login = NOW() 
                    WHERE id = :id';
            
            $this->db->query($sql);
            $this->db->bind(':facebook_id', $fbUserData['id']);
            $this->db->bind(':name', $fbUserData['name']);
            $this->db->bind(':email', $fbUserData['email']);
            
            // Bind facebook_token if provided
            if ($accessToken) {
                $this->db->bind(':facebook_token', $accessToken);
            }
            
            $this->db->bind(':id', $user->id);
            
            try {
                $this->db->execute();
                
                // Log successful Facebook login
                error_log('Successful Facebook login/link: ' . $fbUserData['email'] . ' (ID: ' . $user->id . ')');
                
                // Set login time for session expiration check
                $_SESSION['login_time'] = time();
                $_SESSION['facebook_login'] = true;
                
                // Check if the user has explicitly set a default image (meaning they deleted their profile image)
                $this->db->query('SELECT profile_image FROM users WHERE id = :id');
                $this->db->bind(':id', $user->id);
                $userProfile = $this->db->single();
                
                // Only download the Facebook profile image if the user hasn't explicitly set a default image
                if (!$userProfile || $userProfile->profile_image !== 'public/images/profile.png') {
                    // If we have a profile image URL from Facebook, try to download it
                    if (isset($fbUserData['picture']['data']['url'])) {
                        error_log('User has not explicitly deleted their profile image, downloading from Facebook');
                        $this->downloadFacebookProfileImage($user->id, $fbUserData['picture']['data']['url']);
                    }
                } else {
                    error_log('User has explicitly deleted their profile image, respecting their choice');
                }
                
                return $user->id;
            } catch (Exception $e) {
                error_log('Error updating user with Facebook data: ' . $e->getMessage());
                return false;
            }
        } else {
            // Create new user
            // Generate a random password for the account (they'll use Facebook to login)
            $randomPassword = password_hash(bin2hex(random_bytes(16)), PASSWORD_BCRYPT, ['cost' => 12]);
            
            // Build the SQL query
            $sql = 'INSERT INTO users (
                    facebook_id, 
                    name, 
                    email, 
                    password,';
            
            // Add facebook_token to the insert if provided
            if ($accessToken) {
                $sql .= ' facebook_token,';
            }
            
            $sql .= ' role, 
                    created_at, 
                    last_login
                  ) VALUES (
                    :facebook_id, 
                    :name, 
                    :email, 
                    :password,';
            
            // Add facebook_token placeholder to the insert if provided
            if ($accessToken) {
                $sql .= ' :facebook_token,';
            }
            
            $sql .= ' :role, 
                    NOW(), 
                    NOW()
                  )';
            
            $this->db->query($sql);
            $this->db->bind(':facebook_id', $fbUserData['id']);
            $this->db->bind(':name', $fbUserData['name']);
            $this->db->bind(':email', $fbUserData['email']);
            $this->db->bind(':password', $randomPassword);
            
            // Bind facebook_token if provided
            if ($accessToken) {
                $this->db->bind(':facebook_token', $accessToken);
            }
            
            $this->db->bind(':role', 'user'); // Default role for Facebook users
            
            try {
                if ($this->db->execute()) {
                    $newUserId = $this->db->lastInsertId();
                    
                    // Log successful Facebook registration
                    error_log('New user registered via Facebook: ' . $fbUserData['email'] . ' (ID: ' . $newUserId . ')');
                    
                    // Set login time for session expiration check
                    $_SESSION['login_time'] = time();
                    $_SESSION['facebook_login'] = true;
                    
                    // For new users, we can download the Facebook profile image
                    if (isset($fbUserData['picture']['data']['url'])) {
                        error_log('New user registration, downloading profile image from Facebook');
                        $this->downloadFacebookProfileImage($newUserId, $fbUserData['picture']['data']['url']);
                    }
                    
                    return $newUserId;
                } else {
                    error_log('Failed to create new user via Facebook: ' . $fbUserData['email']);
                    return false;
                }
            } catch (Exception $e) {
                error_log('Exception during Facebook user creation: ' . $e->getMessage());
                return false;
            }
        }
    }
    
    /**
     * Login a user
     * 
     * @param string $email User's email
     * @param string $password User's password
     * @param bool $rememberMe Whether to remember the user
     * @return bool|object False on failure, user object on success
     */
    public function login($email, $password, $rememberMe = false) {
        // Validate inputs
        $email = filter_var(trim($email), FILTER_SANITIZE_EMAIL);
        
        if (empty($email) || empty($password)) {
            return false; // Required fields missing
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return false; // Invalid email format
        }
        
        // Find user by email
        $this->db->query('SELECT id, name, email, password, role, status FROM users WHERE email = :email');
        $this->db->bind(':email', $email);
        $user = $this->db->single();
        
        if (!$user) {
            // Implement timing attack protection
            password_verify('dummy_password', '$2y$10$abcdefghijklmnopqrstuuVzmd4.QxnCbzrWMs4/QqbDTLxKfLxy.');
            return false; // User not found
        }
        
        // Check if user is active
        if (isset($user->status) && $user->status !== 'active') {
            return false; // Account inactive
        }
        
        // Verify password
        if (password_verify($password, $user->password)) {
            // Check if password needs rehashing (if PHP's password hashing algorithm has been updated)
            if (password_needs_rehash($user->password, PASSWORD_BCRYPT, ['cost' => 12])) {
                $newHash = password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
                $this->db->query('UPDATE users SET password = :password WHERE id = :id');
                $this->db->bind(':password', $newHash);
                $this->db->bind(':id', $user->id);
                $this->db->execute();
            }
            
            // Update last login (try-catch in case the column doesn't exist yet)
            try {
                $this->db->query('UPDATE users SET last_login = NOW() WHERE id = :id');
                $this->db->bind(':id', $user->id);
                $this->db->execute();
            } catch (PDOException $e) {
                // Log the error but continue - this is not critical
                error_log('Error updating last_login: ' . $e->getMessage());
                // If the column doesn't exist, we'll just skip this update
            }
            
            // Set login time for session expiration check
            $_SESSION['login_time'] = time();
            
            // Log session creation for debugging
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Login successful - Session created for user ID: ' . $user->id);
                error_log('Session login time set to: ' . $_SESSION['login_time']);
                error_log('Session lifetime: ' . $this->getSessionLifetime() . ' seconds');
            }
            
            // Handle remember me functionality
            if ($rememberMe) {
                $this->createRememberToken($user->id);
            }
            
            // Log successful login
            error_log('Successful login: ' . $email);
            
            return $user;
        } else {
            // Log failed login attempt
            error_log('Failed login attempt: ' . $email);
            return false; // Password incorrect
        }
    }
    
    /**
     * Create a remember me token for a user
     * 
     * @param int $userId User ID
     * @return bool True if token created successfully, false otherwise
     */
    private function createRememberToken($userId) {
        try {
            // Check if remember_tokens table exists
            $tableExists = false;
            try {
                $this->db->query("SHOW TABLES LIKE 'remember_tokens'");
                $tableExists = (bool)$this->db->single();
            } catch (Exception $e) {
                error_log('Error checking if remember_tokens table exists: ' . $e->getMessage());
                return false;
            }
            
            if (!$tableExists) {
                error_log('Remember tokens table does not exist yet - skipping remember me functionality');
                return false;
            }
            
            // Generate a secure random token
            $token = bin2hex(random_bytes(32));
            
            // Get remember me lifetime from settings
            $lifetime = $this->getRememberMeLifetime();
            
            // Calculate expiration date
            $expiresAt = gmdate('Y-m-d H:i:s', time() + $lifetime);
            
            // Delete any existing tokens for this user
            $this->db->query('DELETE FROM remember_tokens WHERE user_id = :user_id');
            $this->db->bind(':user_id', $userId);
            $this->db->execute();
            
            // Insert new token
            $this->db->query('INSERT INTO remember_tokens (user_id, token, expires_at) VALUES (:user_id, :token, :expires_at)');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':token', $token);
            $this->db->bind(':expires_at', $expiresAt);
            
            if ($this->db->execute()) {
                // Set cookie
                setcookie('remember_token', $token, time() + $lifetime, '/', '', isset($_SERVER['HTTPS']), true);
                return true;
            }
        } catch (Exception $e) {
            error_log('Error creating remember token: ' . $e->getMessage());
        }
        
        return false;
    }
    
    /**
     * Check if development bypass is enabled
     * 
     * @return bool
     */
    private function isDevBypassEnabled() {
        static $bypassEnabled = null;
        
        // If we've already checked, return the cached result
        if ($bypassEnabled !== null) {
            return $bypassEnabled;
        }
        
        // Check system_settings table
        try {
            $this->db->query('SELECT setting_value FROM system_settings WHERE setting_key = :key');
            $this->db->bind(':key', 'dev_admin_bypass');
            $result = $this->db->single();
            
            if ($result && $result->setting_value === '1') {
                $bypassEnabled = true;
                return true;
            }
        } catch (Exception $e) {
            error_log('Error checking system_settings table for dev bypass: ' . $e->getMessage());
        }
        
        // Check old settings table as fallback
        try {
            $this->db->query('SELECT value FROM settings WHERE name = :key');
            $this->db->bind(':key', 'dev_admin_bypass');
            $oldResult = $this->db->single();
            
            if ($oldResult && $oldResult->value === '1') {
                $bypassEnabled = true;
                return true;
            }
        } catch (Exception $e) {
            error_log('Error checking old settings table for dev bypass: ' . $e->getMessage());
        }
        
        $bypassEnabled = false;
        return false;
    }
    
    /**
     * Check if user is logged in
     * 
     * @return bool
     */
    public function isLoggedIn() {
        // First check if dev bypass is enabled
        if ($this->isDevBypassEnabled()) {
            // If dev bypass is enabled, automatically consider user as logged in
            // Set session variables if they don't exist
            if (!isset($_SESSION['user_id'])) {
                $_SESSION['user_id'] = 0; // Special dev bypass ID
                $_SESSION['user_name'] = 'Developer';
                $_SESSION['user_email'] = '<EMAIL>';
                $_SESSION['user_role'] = 'admin';
            }
            return true;
        }
        
        // Normal login check
        return isset($_SESSION['user_id']);
    }
    
    /**
     * Get current user ID
     * 
     * @return int|null
     */
    public function getCurrentUserId() {
        return $this->isLoggedIn() ? $_SESSION['user_id'] : null;
    }
    
    /**
     * Get current user role
     * 
     * @return string|null
     */
    public function getCurrentUserRole() {
        // First check if dev bypass is enabled
        if ($this->isDevBypassEnabled()) {
            // If dev bypass is enabled, always return admin role
            return 'admin';
        }
        
        // Normal role check
        return $this->isLoggedIn() ? $_SESSION['user_role'] : null;
    }
    
    /**
     * Get role hierarchy level
     * 
     * @param string $role Role to check
     * @return int Role hierarchy level (higher number = higher privileges)
     */
    private function getRoleHierarchyLevel($role) {
        $roleHierarchy = [
            'user' => 1,
            'judge' => 2,
            'staff' => 2, // Same level as judge, different permissions
            'coordinator' => 3,
            'admin' => 4
        ];
        
        return isset($roleHierarchy[$role]) ? $roleHierarchy[$role] : 0;
    }
    
    /**
     * Check if current user has a specific role or higher role in hierarchy
     * 
     * @param string|array $roles Role or array of roles to check
     * @param bool $exactMatch If true, requires exact role match (no hierarchy)
     * @return bool
     */
    public function hasRole($roles, $exactMatch = false) {
        // First check for development bypass for admin role
        if (($roles === 'admin' || (is_array($roles) && in_array('admin', $roles))) && $this->isDevBypassEnabled()) {
            // If dev bypass is enabled and checking for admin role, automatically grant access
            return true;
        }
        
        // If dev bypass is not enabled or not checking for admin role, check normal login
        if ($this->isLoggedIn()) {
            $userRole = $_SESSION['user_role'];
            $userRoleLevel = $this->getRoleHierarchyLevel($userRole);
            
            if ($exactMatch) {
                // Exact match mode - original behavior
                if (is_array($roles)) {
                    return in_array($userRole, $roles);
                } else {
                    return $userRole === $roles;
                }
            } else {
                // Hierarchical mode - check if user has required role or higher
                if (is_array($roles)) {
                    // Get the highest required role level from the array
                    $highestRequiredLevel = 0;
                    foreach ($roles as $role) {
                        $roleLevel = $this->getRoleHierarchyLevel($role);
                        $highestRequiredLevel = max($highestRequiredLevel, $roleLevel);
                        
                        // If user has exact match with any role, return true immediately
                        if ($userRole === $role) {
                            return true;
                        }
                    }
                    
                    // Check if user's role level is higher than any required role
                    return $userRoleLevel >= $highestRequiredLevel;
                } else {
                    // Single role check - either exact match or higher role
                    $requiredRoleLevel = $this->getRoleHierarchyLevel($roles);
                    return $userRoleLevel >= $requiredRoleLevel;
                }
            }
        }
        
        // If we get here, user doesn't have the required role
        return false;
    }
    
    /**
     * Check if user has a specific role for a show
     *
     * @param int $showId Show ID
     * @param string $role Role to check
     * @return bool True if user has the role, false otherwise
     */
    public function hasShowRole($showId, $role) {
        // Admin has all roles
        if ($this->hasRole('admin')) {
            return true;
        }

        // Check primary role first
        $currentRole = $this->getCurrentUserRole();
        $userId = $this->getCurrentUserId();

        if (!$userId) {
            return false;
        }

        // Coordinator has all roles for their shows
        if ($currentRole === 'coordinator') {
            $this->db->query('SELECT COUNT(*) as count FROM shows WHERE id = :show_id AND coordinator_id = :user_id');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':user_id', $userId);
            $result = $this->db->single();

            if ($result && $result->count > 0) {
                return true;
            }
        }

        // Check if user has the primary role
        if ($currentRole === $role) {
            // For judge role, check if assigned to show
            if ($role === 'judge') {
                $this->db->query('SELECT COUNT(*) as count FROM judge_assignments
                                  WHERE show_id = :show_id AND judge_id = :user_id');
                $this->db->bind(':show_id', $showId);
                $this->db->bind(':user_id', $userId);
                $result = $this->db->single();
                return $result && $result->count > 0;
            }

            // For staff role, check if assigned to show (old system)
            if ($role === 'staff') {
                $this->db->query('SELECT COUNT(*) as count FROM staff_assignments
                                  WHERE show_id = :show_id AND staff_id = :user_id');
                $this->db->bind(':show_id', $showId);
                $this->db->bind(':user_id', $userId);
                $result = $this->db->single();
                if ($result && $result->count > 0) {
                    return true;
                }
            }
        }

        // Check show-specific role assignments (new system)
        $this->db->query('SELECT COUNT(*) as count FROM show_role_assignments
                          WHERE show_id = :show_id AND user_id = :user_id AND assigned_role = :role
                          AND is_active = 1 AND expires_at > NOW()');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':role', $role);
        $result = $this->db->single();

        return $result && $result->count > 0;
    }

    /**
     * Get all roles for a user in a show
     *
     * @param int $showId Show ID
     * @param int $userId User ID (optional, defaults to current user)
     * @return array Roles
     */
    public function getUserShowRoles($showId, $userId = null) {
        if ($userId === null) {
            $userId = $this->getCurrentUserId();
        }

        if (!$userId) {
            return [];
        }

        $roles = [];

        // Admin has all roles
        if ($this->hasRole('admin')) {
            return ['admin', 'coordinator', 'judge', 'staff'];
        }

        // Check primary role
        $currentRole = $this->getCurrentUserRole();

        // Coordinator has all roles for their shows
        if ($currentRole === 'coordinator') {
            $this->db->query('SELECT COUNT(*) as count FROM shows WHERE id = :show_id AND coordinator_id = :user_id');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':user_id', $userId);
            $result = $this->db->single();

            if ($result && $result->count > 0) {
                return ['coordinator', 'judge', 'staff'];
            }
        }

        // Add primary role if user has show access
        if ($this->hasShowRole($showId, $currentRole)) {
            $roles[] = $currentRole;
        }

        // Get show-specific role assignments
        $this->db->query('SELECT DISTINCT assigned_role FROM show_role_assignments
                          WHERE show_id = :show_id AND user_id = :user_id
                          AND is_active = 1 AND expires_at > NOW()');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':user_id', $userId);
        $showRoles = $this->db->resultSet();

        foreach ($showRoles as $roleObj) {
            if (!in_array($roleObj->assigned_role, $roles)) {
                $roles[] = $roleObj->assigned_role;
            }
        }

        return $roles;
    }

    /**
     * Check if user is assigned to a show in any capacity
     *
     * @param int $showId Show ID
     * @param int $userId User ID (optional, defaults to current user)
     * @return bool
     */
    public function isAssignedToShow($showId, $userId = null) {
        if ($userId === null) {
            $userId = $this->getCurrentUserId();
        }

        if (!$userId) {
            return false;
        }

        // Admin has access to all shows
        if ($this->hasRole('admin')) {
            return true;
        }

        // Check if user is coordinator of the show
        $this->db->query('SELECT COUNT(*) as count FROM shows WHERE id = :show_id AND coordinator_id = :user_id');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':user_id', $userId);
        $result = $this->db->single();

        if ($result && $result->count > 0) {
            return true;
        }

        // Check show-specific assignments
        $this->db->query('SELECT COUNT(*) as count FROM show_role_assignments
                          WHERE show_id = :show_id AND user_id = :user_id
                          AND is_active = 1 AND expires_at > NOW()');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':user_id', $userId);
        $result = $this->db->single();

        if ($result && $result->count > 0) {
            return true;
        }

        // Check old staff assignments
        $this->db->query('SELECT COUNT(*) as count FROM staff_assignments
                          WHERE show_id = :show_id AND staff_id = :user_id');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':user_id', $userId);
        $result = $this->db->single();

        if ($result && $result->count > 0) {
            return true;
        }

        // Check judge assignments
        $this->db->query('SELECT COUNT(*) as count FROM judge_assignments
                          WHERE show_id = :show_id AND judge_id = :user_id');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':user_id', $userId);
        $result = $this->db->single();

        return $result && $result->count > 0;
    }

    /**
     * Logout the current user
     *
     * @return void
     */
    public function logout() {
        // Get user ID before destroying session
        $userId = $_SESSION['user_id'] ?? null;

        // Clear session variables
        unset($_SESSION['user_id']);
        unset($_SESSION['user_role']);
        unset($_SESSION['user_name']);
        unset($_SESSION['user_email']);
        unset($_SESSION['login_time']);
        unset($_SESSION['facebook_login']);
        
        // Destroy session
        session_destroy();
        
        // Check if remember_tokens table exists
        $tableExists = false;
        try {
            $this->db->query("SHOW TABLES LIKE 'remember_tokens'");
            $tableExists = (bool)$this->db->single();
        } catch (Exception $e) {
            error_log('Error checking if remember_tokens table exists: ' . $e->getMessage());
        }
        
        if ($tableExists) {
            // Clear remember me cookie if exists
            if (isset($_COOKIE['remember_token'])) {
                // Delete the token from database
                try {
                    $this->db->query('DELETE FROM remember_tokens WHERE token = :token');
                    $this->db->bind(':token', $_COOKIE['remember_token']);
                    $this->db->execute();
                } catch (Exception $e) {
                    error_log('Error deleting remember token: ' . $e->getMessage());
                }
                
                // Expire the cookie
                setcookie('remember_token', '', time() - 3600, '/', '', isset($_SERVER['HTTPS']), true);
            }
            
            // If we have a user ID, delete all remember tokens for this user
            if ($userId) {
                try {
                    $this->db->query('DELETE FROM remember_tokens WHERE user_id = :user_id');
                    $this->db->bind(':user_id', $userId);
                    $this->db->execute();
                } catch (Exception $e) {
                    error_log('Error deleting user remember tokens: ' . $e->getMessage());
                }
            }
        } else {
            // Just expire the cookie if the table doesn't exist yet
            if (isset($_COOKIE['remember_token'])) {
                setcookie('remember_token', '', time() - 3600, '/', '', isset($_SERVER['HTTPS']), true);
            }
        }
    }
    
    /**
     * Generate a password reset token
     * 
     * @param string $email User's email
     * @return bool|string False on failure, token on success
     */
    public function generatePasswordResetToken($email) {
        // Find user by email
        $this->db->query('SELECT id FROM users WHERE email = :email');
        $this->db->bind(':email', $email);
        $user = $this->db->single();
        
        if (!$user) {
            return false; // User not found
        }
        
        // Generate token
        $token = bin2hex(random_bytes(32));
        $expires = gmdate('Y-m-d H:i:s', time() + 3600); // 1 hour expiration
        
        // Store token in database
        $this->db->query('INSERT INTO password_resets (user_id, token, expires_at, created_at) 
                          VALUES (:user_id, :token, :expires_at, NOW())');
        $this->db->bind(':user_id', $user->id);
        $this->db->bind(':token', $token);
        $this->db->bind(':expires_at', $expires);
        
        if ($this->db->execute()) {
            return $token;
        } else {
            return false;
        }
    }
    
    /**
     * Verify a password reset token
     * 
     * @param string $token Reset token
     * @return bool|int False on failure, user ID on success
     */
    public function verifyPasswordResetToken($token) {
        $this->db->query('SELECT user_id FROM password_resets 
                          WHERE token = :token AND expires_at > NOW() 
                          ORDER BY created_at DESC LIMIT 1');
        $this->db->bind(':token', $token);
        $result = $this->db->single();
        
        return $result ? $result->user_id : false;
    }
    
    /**
     * Reset a user's password
     * 
     * @param int $userId User ID
     * @param string $password New password
     * @return bool
     */
    public function resetPassword($userId, $password) {
        // Hash password
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        // Update password
        $this->db->query('UPDATE users SET password = :password WHERE id = :id');
        $this->db->bind(':password', $hashedPassword);
        $this->db->bind(':id', $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Download and save Facebook profile image
     * 
     * @param int $userId User ID
     * @param string $imageUrl Facebook profile image URL
     * @return bool True on success, false on failure
     */
    private function downloadFacebookProfileImage($userId, $imageUrl) {
        try {
            error_log('Auth::downloadFacebookProfileImage - Downloading image for user ' . $userId . ' from ' . $imageUrl);
            
            // Set a user agent to avoid potential blocking
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => [
                        'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Accept: image/jpeg,image/png,image/*,*/*'
                    ]
                ]
            ]);
            
            // Download the image
            $imageContent = @file_get_contents($imageUrl, false, $context);
            
            if (!$imageContent) {
                error_log('Auth::downloadFacebookProfileImage - Failed to download image with file_get_contents, trying cURL');
                
                // Try with cURL as a fallback
                $ch = curl_init($imageUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
                curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: image/jpeg,image/png,image/*,*/*']);
                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // Follow redirects
                $imageContent = curl_exec($ch);
                
                if (curl_errno($ch)) {
                    error_log('Auth::downloadFacebookProfileImage - cURL error: ' . curl_error($ch));
                    curl_close($ch);
                    return false;
                }
                
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($httpCode != 200 || empty($imageContent)) {
                    error_log('Auth::downloadFacebookProfileImage - HTTP error: ' . $httpCode);
                    return false;
                }
            }
            
            // Verify the image content is valid
            if (strlen($imageContent) < 100) {
                error_log('Auth::downloadFacebookProfileImage - Downloaded image is too small: ' . strlen($imageContent) . ' bytes');
                return false;
            }
            
            // Check if the content is a valid image
            $tempImageCheck = @imagecreatefromstring($imageContent);
            if ($tempImageCheck === false) {
                error_log('Auth::downloadFacebookProfileImage - Downloaded content is not a valid image');
                return false;
            }
            imagedestroy($tempImageCheck);
            
            // Create a temporary file
            $tempFile = tempnam(sys_get_temp_dir(), 'fb_img_');
            $bytesWritten = file_put_contents($tempFile, $imageContent);
            
            if ($bytesWritten === false) {
                error_log('Auth::downloadFacebookProfileImage - Failed to write to temporary file: ' . $tempFile);
                return false;
            }
            
            // Create a file array similar to $_FILES
            $fileArray = [
                'name' => 'facebook_profile.jpg',
                'type' => 'image/jpeg',
                'tmp_name' => $tempFile,
                'error' => 0,
                'size' => filesize($tempFile)
            ];
            
            // Load image editor model
            require_once APPROOT . '/models/ImageEditorModel.php';
            $imageEditorModel = new ImageEditorModel();
            
            // First, delete any existing profile images for this user
            $this->db->query('SELECT id FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id');
            $this->db->bind(':entity_type', 'user');
            $this->db->bind(':entity_id', $userId);
            $existingImages = $this->db->resultSet();
            
            // Delete each existing image
            foreach ($existingImages as $image) {
                $imageEditorModel->deleteImage($image->id);
            }
            
            // Process the image upload
            $uploadDir = 'uploads/users/';
            
            // Create directory if it doesn't exist
            if (!file_exists($uploadDir)) {
                if (!mkdir($uploadDir, 0755, true)) {
                    error_log('Auth::downloadFacebookProfileImage - Failed to create upload directory: ' . $uploadDir);
                    @unlink($tempFile);
                    return false;
                }
            }
            
            // Process the image upload
            $imageData = $imageEditorModel->processImageUpload(
                $fileArray, 
                'user', 
                $userId, 
                $uploadDir,
                $userId,
                true // Set as primary image
            );
            
            // Clean up the temporary file
            @unlink($tempFile);
            
            if ($imageData) {
                error_log('Auth::downloadFacebookProfileImage - Successfully processed image upload for user ' . $userId);
                
                // Clear the profile_image field in the users table to avoid confusion
                // This ensures we only use the images table for profile images
                $this->db->query('UPDATE users SET profile_image = NULL WHERE id = :id');
                $this->db->bind(':id', $userId);
                $this->db->execute();
                
                return true;
            } else {
                error_log('Auth::downloadFacebookProfileImage - Failed to process image upload for user ' . $userId);
                return false;
            }
            
        } catch (Exception $e) {
            error_log('Auth::downloadFacebookProfileImage - Exception: ' . $e->getMessage());
            return false;
        }
    }
}