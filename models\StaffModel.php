<?php
/**
 * Staff Model
 * 
 * This model handles all database operations related to staff members.
 */
class StaffModel {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get all staff members
     * 
     * @return array
     */
    public function getAllStaff() {
        $this->db->query('SELECT id, name, email, profile_image, phone, created_at, last_login, status 
                          FROM users WHERE role = :role');
        $this->db->bind(':role', 'staff');
        
        return $this->db->resultSet();
    }
    
    /**
     * Assign staff to a show
     * 
     * @param int $staffId Staff user ID
     * @param int $showId Show ID
     * @param int $assignedBy User ID of the admin/coordinator who made the assignment
     * @return bool
     */
    public function assignStaffToShow($staffId, $showId, $assignedBy) {
        // First check if this assignment already exists
        $this->db->query('SELECT id FROM staff_assignments 
                          WHERE staff_id = :staff_id AND show_id = :show_id');
        $this->db->bind(':staff_id', $staffId);
        $this->db->bind(':show_id', $showId);
        $existing = $this->db->single();
        
        if ($existing) {
            // Assignment already exists, return true
            return true;
        }
        
        // Verify that the staff user exists (since we might not have foreign key constraints)
        $this->db->query('SELECT id FROM users WHERE id = :id AND role = :role');
        $this->db->bind(':id', $staffId);
        $this->db->bind(':role', 'staff');
        $staffUser = $this->db->single();
        
        if (!$staffUser) {
            // Staff user doesn't exist or doesn't have the staff role
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("StaffModel::assignStaffToShow - Staff user {$staffId} doesn't exist or doesn't have the staff role");
            }
            return false;
        }
        
        // Verify that the show exists
        $this->db->query('SELECT id FROM shows WHERE id = :id');
        $this->db->bind(':id', $showId);
        $show = $this->db->single();
        
        if (!$show) {
            // Show doesn't exist
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("StaffModel::assignStaffToShow - Show {$showId} doesn't exist");
            }
            return false;
        }
        
        // Verify that the assigner exists
        $this->db->query('SELECT id FROM users WHERE id = :id');
        $this->db->bind(':id', $assignedBy);
        $assigner = $this->db->single();
        
        if (!$assigner) {
            // Assigner doesn't exist
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("StaffModel::assignStaffToShow - Assigner {$assignedBy} doesn't exist");
            }
            return false;
        }
        
        // Create new assignment
        $this->db->query('INSERT INTO staff_assignments (staff_id, show_id, assigned_by, created_at) 
                          VALUES (:staff_id, :show_id, :assigned_by, NOW())');
        $this->db->bind(':staff_id', $staffId);
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':assigned_by', $assignedBy);
        
        return $this->db->execute();
    }
    
    /**
     * Remove staff assignment from a show
     * 
     * @param int $staffId Staff user ID
     * @param int $showId Show ID
     * @return bool
     */
    public function removeStaffFromShow($staffId, $showId) {
        // First check if this assignment exists
        $this->db->query('SELECT id FROM staff_assignments 
                          WHERE staff_id = :staff_id AND show_id = :show_id');
        $this->db->bind(':staff_id', $staffId);
        $this->db->bind(':show_id', $showId);
        $existing = $this->db->single();
        
        if (!$existing) {
            // Assignment doesn't exist, log and return true (already removed)
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("StaffModel::removeStaffFromShow - Assignment for staff {$staffId} and show {$showId} doesn't exist");
            }
            return true;
        }
        
        // Delete the assignment
        $this->db->query('DELETE FROM staff_assignments 
                          WHERE staff_id = :staff_id AND show_id = :show_id');
        $this->db->bind(':staff_id', $staffId);
        $this->db->bind(':show_id', $showId);
        
        return $this->db->execute();
    }
    
    /**
     * Get all shows assigned to a staff member
     * 
     * @param int $staffId Staff user ID
     * @return array
     */
    public function getAssignedShows($staffId) {
        $this->db->query('SELECT s.* 
                          FROM shows s
                          JOIN staff_assignments sa ON s.id = sa.show_id
                          WHERE sa.staff_id = :staff_id
                          ORDER BY s.start_date DESC');
        $this->db->bind(':staff_id', $staffId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get all staff assigned to a show
     * 
     * @param int $showId Show ID
     * @return array
     */
    public function getShowStaff($showId) {
        $this->db->query('SELECT u.id, u.name, u.email, u.profile_image, u.phone, 
                          sa.created_at as assigned_at, a.name as assigned_by_name
                          FROM users u
                          JOIN staff_assignments sa ON u.id = sa.staff_id
                          JOIN users a ON sa.assigned_by = a.id
                          WHERE sa.show_id = :show_id
                          ORDER BY u.name');
        $this->db->bind(':show_id', $showId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Check if a staff member is assigned to a show
     *
     * @param int $staffId Staff user ID
     * @param int $showId Show ID
     * @return bool
     */
    public function isAssignedToShow($staffId, $showId) {
        // Check old staff assignments table
        $this->db->query('SELECT id FROM staff_assignments
                          WHERE staff_id = :staff_id AND show_id = :show_id');
        $this->db->bind(':staff_id', $staffId);
        $this->db->bind(':show_id', $showId);

        $result = $this->db->single();

        if ($result) {
            return true;
        }

        // Check new show role assignments table
        $this->db->query('SELECT id FROM show_role_assignments
                          WHERE user_id = :staff_id AND show_id = :show_id AND assigned_role = "staff"
                          AND is_active = 1 AND expires_at > NOW()');
        $this->db->bind(':staff_id', $staffId);
        $this->db->bind(':show_id', $showId);

        $result = $this->db->single();

        return $result ? true : false;
    }
    
    /**
     * Get all available staff (not yet assigned to a specific show)
     * 
     * @param int $showId Show ID
     * @return array
     */
    public function getAvailableStaff($showId) {
        $this->db->query('SELECT u.id, u.name, u.email, u.profile_image, u.phone
                          FROM users u
                          WHERE u.role = :role
                          AND u.id NOT IN (
                              SELECT sa.staff_id 
                              FROM staff_assignments sa 
                              WHERE sa.show_id = :show_id
                          )
                          ORDER BY u.name');
        $this->db->bind(':role', 'staff');
        $this->db->bind(':show_id', $showId);
        
        return $this->db->resultSet();
    }

    /**
     * Get staff assignment counts for dashboard
     *
     * @param int $staffId Staff ID
     * @return array Assignment counts
     */
    public function getStaffAssignmentCounts($staffId) {
        try {
            $counts = [
                'total_assignments' => 0,
                'upcoming_assignments' => 0,
                'active_assignments' => 0,
                'completed_assignments' => 0
            ];

            // Get total assignments count
            $this->db->query('SELECT COUNT(*) as total FROM staff_assignments WHERE staff_id = :staff_id');
            $this->db->bind(':staff_id', $staffId);
            $totalResult = $this->db->single();
            $counts['total_assignments'] = $totalResult->total ?? 0;

            // Get counts by status
            $this->db->query('SELECT
                             SUM(CASE WHEN s.start_date > NOW() THEN 1 ELSE 0 END) as upcoming_assignments,
                             SUM(CASE WHEN s.start_date <= NOW() AND s.end_date >= NOW() THEN 1 ELSE 0 END) as active_assignments,
                             SUM(CASE WHEN s.end_date < NOW() THEN 1 ELSE 0 END) as completed_assignments
                             FROM staff_assignments sa
                             JOIN shows s ON sa.show_id = s.id
                             WHERE sa.staff_id = :staff_id');
            $this->db->bind(':staff_id', $staffId);
            $statusResult = $this->db->single();

            if ($statusResult) {
                $counts['upcoming_assignments'] = $statusResult->upcoming_assignments ?? 0;
                $counts['active_assignments'] = $statusResult->active_assignments ?? 0;
                $counts['completed_assignments'] = $statusResult->completed_assignments ?? 0;
            }

            return $counts;

        } catch (Exception $e) {
            error_log('Error in StaffModel::getStaffAssignmentCounts: ' . $e->getMessage());
            return [
                'total_assignments' => 0,
                'upcoming_assignments' => 0,
                'active_assignments' => 0,
                'completed_assignments' => 0
            ];
        }
    }

    /**
     * Get staff statistics for dashboard
     *
     * @param int $staffId Staff ID
     * @return array Staff statistics
     */
    public function getStaffStats($staffId) {
        try {
            $stats = [
                'this_month_shows' => 0,
                'total_hours' => 0,
                'next_show' => null
            ];

            // Get this month's shows
            $this->db->query('SELECT COUNT(*) as this_month_shows
                             FROM staff_assignments sa
                             JOIN shows s ON sa.show_id = s.id
                             WHERE sa.staff_id = :staff_id
                             AND MONTH(s.start_date) = MONTH(NOW())
                             AND YEAR(s.start_date) = YEAR(NOW())');
            $this->db->bind(':staff_id', $staffId);
            $monthResult = $this->db->single();
            $stats['this_month_shows'] = $monthResult->this_month_shows ?? 0;

            // Calculate total hours (estimate based on show duration)
            $this->db->query('SELECT SUM(TIMESTAMPDIFF(HOUR, s.start_date, s.end_date)) as total_hours
                             FROM staff_assignments sa
                             JOIN shows s ON sa.show_id = s.id
                             WHERE sa.staff_id = :staff_id AND s.end_date < NOW()');
            $this->db->bind(':staff_id', $staffId);
            $hoursResult = $this->db->single();
            $stats['total_hours'] = $hoursResult->total_hours ?? 0;

            // Get next upcoming show
            $this->db->query('SELECT s.id, s.name, s.location, s.start_date
                             FROM staff_assignments sa
                             JOIN shows s ON sa.show_id = s.id
                             WHERE sa.staff_id = :staff_id AND s.start_date > NOW()
                             ORDER BY s.start_date ASC
                             LIMIT 1');
            $this->db->bind(':staff_id', $staffId);
            $nextShow = $this->db->single();

            if ($nextShow) {
                $stats['next_show'] = [
                    'id' => $nextShow->id,
                    'name' => $nextShow->name,
                    'location' => $nextShow->location,
                    'start_date' => $nextShow->start_date
                ];
            }

            return $stats;

        } catch (Exception $e) {
            error_log('Error in StaffModel::getStaffStats: ' . $e->getMessage());
            return [
                'this_month_shows' => 0,
                'total_hours' => 0,
                'next_show' => null
            ];
        }
    }

    /**
     * Get paginated staff assignments
     *
     * @param int $staffId Staff ID
     * @param int $page Page number (1-based)
     * @param int $perPage Records per page
     * @param string $search Search term
     * @param string $statusFilter Status filter
     * @param string $dateFilter Date filter
     * @param string $orderBy Order by field
     * @param string $orderDir Order direction (ASC/DESC)
     * @return array Paginated results with metadata
     */
    public function getPaginatedStaffAssignments($staffId, $page = 1, $perPage = 20, $search = '', $statusFilter = 'all', $dateFilter = '', $orderBy = 'start_date', $orderDir = 'DESC') {
        $startTime = microtime(true);

        try {
            // Validate parameters
            $page = max(1, (int)$page);
            $perPage = min(100, max(1, (int)$perPage));
            $offset = ($page - 1) * $perPage;

            // Validate order by field
            $allowedOrderFields = ['start_date', 'end_date', 'show_name', 'location'];
            if (!in_array($orderBy, $allowedOrderFields)) {
                $orderBy = 'start_date';
            }

            $orderDir = strtoupper($orderDir) === 'ASC' ? 'ASC' : 'DESC';

            // Build WHERE conditions
            $whereConditions = ['sa.staff_id = :staff_id'];
            $bindParams = ['staff_id' => $staffId];

            // Search filter
            if (!empty($search)) {
                $whereConditions[] = '(s.name LIKE :search_name OR s.location LIKE :search_location)';
                $bindParams['search_name'] = '%' . $search . '%';
                $bindParams['search_location'] = '%' . $search . '%';
            }

            // Status filter
            if ($statusFilter !== 'all') {
                switch ($statusFilter) {
                    case 'upcoming':
                        $whereConditions[] = 's.start_date > NOW()';
                        break;
                    case 'active':
                        $whereConditions[] = 's.start_date <= NOW() AND s.end_date >= NOW()';
                        break;
                    case 'completed':
                        $whereConditions[] = 's.end_date < NOW()';
                        break;
                }
            }

            // Date filter
            if (!empty($dateFilter)) {
                switch ($dateFilter) {
                    case 'this_month':
                        $whereConditions[] = 'MONTH(s.start_date) = MONTH(NOW()) AND YEAR(s.start_date) = YEAR(NOW())';
                        break;
                    case 'next_month':
                        $whereConditions[] = 'MONTH(s.start_date) = MONTH(NOW() + INTERVAL 1 MONTH) AND YEAR(s.start_date) = YEAR(NOW() + INTERVAL 1 MONTH)';
                        break;
                    case 'this_year':
                        $whereConditions[] = 'YEAR(s.start_date) = YEAR(NOW())';
                        break;
                }
            }

            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

            // Count total records
            $countQuery = "SELECT COUNT(*) as total
                          FROM staff_assignments sa
                          JOIN shows s ON sa.show_id = s.id
                          $whereClause";

            $this->db->query($countQuery);
            foreach ($bindParams as $key => $value) {
                $this->db->bind(':' . $key, $value);
            }

            $totalResult = $this->db->single();
            $totalRecords = $totalResult->total ?? 0;

            // Get paginated data
            $dataQuery = "SELECT sa.*, s.name as show_name, s.location, s.start_date, s.end_date, s.status,
                         sa.role
                         FROM staff_assignments sa
                         JOIN shows s ON sa.show_id = s.id
                         $whereClause
                         ORDER BY s.$orderBy $orderDir
                         LIMIT :limit OFFSET :offset";

            $this->db->query($dataQuery);
            foreach ($bindParams as $key => $value) {
                $this->db->bind(':' . $key, $value);
            }
            $this->db->bind(':limit', $perPage, PDO::PARAM_INT);
            $this->db->bind(':offset', $offset, PDO::PARAM_INT);

            $assignments = $this->db->resultSet();

            // Calculate pagination metadata
            $totalPages = ceil($totalRecords / $perPage);
            $startRecord = $totalRecords > 0 ? $offset + 1 : 0;
            $endRecord = min($offset + $perPage, $totalRecords);

            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 1);

            error_log("StaffModel::getPaginatedStaffAssignments - Staff: $staffId, Page: $page, Assignments: " . count($assignments) . ", Time: {$executionTime}ms");

            return [
                'assignments' => $assignments,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total_pages' => $totalPages,
                    'total_assignments' => $totalRecords,
                    'start_record' => $startRecord,
                    'end_record' => $endRecord,
                    'has_prev' => $page > 1,
                    'has_next' => $page < $totalPages
                ]
            ];

        } catch (Exception $e) {
            error_log('Error in StaffModel::getPaginatedStaffAssignments: ' . $e->getMessage());
            return [
                'assignments' => [],
                'pagination' => [
                    'current_page' => 1,
                    'per_page' => $perPage,
                    'total_pages' => 0,
                    'total_assignments' => 0,
                    'start_record' => 0,
                    'end_record' => 0,
                    'has_prev' => false,
                    'has_next' => false
                ]
            ];
        }
    }
}